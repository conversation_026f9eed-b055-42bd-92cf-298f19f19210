package integration

import (
	"net/http"
	"os"
	"strings"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestTechInternet(t *testing.T) {
	t.<PERSON>llel()
	var tests = []fastly.Test{
		fastly.Status{
			Request: fastly.Request{
				Description: "tech/internet/ gets a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tech/internet/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "tech/internet/ redirect gets 301",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/tech/internet/best-internet-options-for-international-travel/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/tech/internet/",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		/*
			fastly.Header{
				Request: fastly.Request{
					Description: "gets a 200 status along with proper headers for feed.json",
					Method:      "GET",
					Scheme:      "https://",
					Host:        "www.usatoday.com",
					Path:        "/tech/internet/_next/static/chunks/fd9d1056-98642a3531fd7040.js",
					UA:          UserAgentMacOSChrome,
					Referer:     "",
					Cookies:     []*http.Cookie{},
					Headers:     http.Header{},
				},
				Status: http.StatusOK,
				Headers: http.Header{
					"Gannett-Debug-Path": []string{
						"ttl: 31536000.000",
					},
					"Cache-Control": []string{
						"public, max-age=31536000, immutable",
					},
					"Surrogate-Key": []string{
						"/tech/internet",
					},
				},
				MatchType: fastly.PartialMatch{},
			},
		*/
	}
	var env = os.Getenv("ENVIRONMENT")
	for _, test := range tests {
		if strings.Contains(env, "origin-staging") {
			continue
		}
		test.Execute(t)
	}
}

package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestXPRMedia(t *testing.T) {
	t.<PERSON>()

	var tests = []fastly.Test{
		// xpr media landing page
		fastly.Header{
			Request: fastly.Request{
				Description: "/press-release gets a 200 status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/press-release/",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"xpr_media backend:set",
				},
				"Surrogate-Key": []string{
					"/press-release",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}

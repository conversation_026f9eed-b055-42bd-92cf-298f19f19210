package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestBotDetection(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// bot-detection endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "bot-detection gets a 200 status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/bot-detection",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"bot-detection backend",
				},
				"Surrogate-Key": []string{
					"bot-detection",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}

package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestRealEstate(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// Real Estate endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "/real-estate gets a 200 status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/real-estate",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"realestate backend:set",
				},
				"Surrogate-Key": []string{
					"/real-estate",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}

package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestRecAI(t *testing.T) {
	t.<PERSON>llel()
	var tests = []fastly.Test{
		// rec-ai User Event proxy endpoints
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-user event proxying - encoded url",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUvb0YzczUvcC9odHRwcyUzQSUyRiUyRnd3dy51c2F0b2RheS5jb20lMkYvJTdCJTIydmlzaXRvcklkJTIyJTNBJTIyYzFkNTZjNGUtZGI4NS0xMWViLTg2YzItMGFhNjYyOGM2ZjNiJTIyJTJDJTIyZXZlbnRUeXBlJTIyJTNBJTIyaG9tZS1wYWdlLXZpZXclMjIlMkMlMjJyZWZlcnJlclVyaSUyMiUzQSUyMiUyMiUyQyUyMnVzZXJJbmZvJTIyJTNBJTdCJTIydXNlcklkJTIyJTNBJTIyZTU3ZDk4MjAtMjg2Yy00Y2EwLThkMjYtNmEwMDgxYzAyMWEyJTIyJTdEJTdE",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-user event proxying - decoded url",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/oF3s5/p/https%3A%2F%2Fwww.usatoday.com%2F/%7B%22visitorId%22%3A%22c1d56c4e-db85-11eb-86c2-0aa6628c6f3b%22%2C%22eventType%22%3A%22home-page-view%22%2C%22referrerUri%22%3A%22%22%2C%22userInfo%22%3A%7B%22userId%22%3A%22e57d9820-286c-4ca0-8d26-6a0081c021a2%22%7D%7D",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		// desktop rec-ai endpoints
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute with encode url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUveDFUR1RDL1VTQVQvMTIzNDQyMjI0NTUyL2MxZDU2YzRlLWRiODUtMTFlYi04NmMyLTBhYTY2MjhjNmYzYi8xLzFfMS8K",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute with decode url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTC/USAT/123442224552/c1d56c4e-db85-11eb-86c2-0aa6628c6f3b/1/1_1/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "rec-ai-compute client header check",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUveDFUR1RDL1VTQVQvMTIzNDQyMjI0NTUyL2MxZDU2YzRlLWRiODUtMTFlYi04NmMyLTBhYTY2MjhjNmYzYi8xLzFfMS8K",
				UA:          UserAgentWindowsChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Content-Security-Policy": []string{
					"upgrade-insecure-requests;frame-ancestors 'none';default-src 'none'",
				},
				"Feature-Policy": []string{
					"autoplay 'none';camera 'none';display-capture 'none';encrypted-media 'none';fullscreen 'none';geolocation 'none';microphone 'none';midi 'none';payment 'none';picture-in-picture 'none';publickey-credentials-get 'none';sync-xhr 'none';usb 'none';xr-spatial-tracking 'none'",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// block bot requests to rec-AI endpoints
		fastly.Status{
			Request: fastly.Request{
				Description: "should 404 when bots request rec-ai prediction point",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUveDFUR1RDL1VTQVQvMTIzNDQyMjI0NTUyL2MxZDU2YzRlLWRiODUtMTFlYi04NmMyLTBhYTY2MjhjNmYzYi8xLzFfMS8K",
				UA:          UserAgentGoogleBotDesktop,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusNotFound,
		},
		// Native Rec AI endpoints
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute native-up-next with decode url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTCFgd3ZA/USAT/123442224552/c1d56c4e-db85-11eb-86c2-0aa6628c6f3b/1/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute native-recommended-for-you with decode url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTCGh3T21/USAT/c1d56c4e-db85-11eb-86c2-0aa6628c6f3b/1/10/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		// up-next-es endpoint
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute up-next-es with encoded url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUveDFUR1RDRWY0RXM2LzEyMzQvMC8xXzEvbmV3cy8",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute up-next-es with decoded url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTCEf4Es6/1234/0/1_1/news/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "rec-ai-compute up-next-es returns the expected TTL + cache-control",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTCEf4Es6/1234/0/1_1/news/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path-Full": []string{
					"ttl: 30.000",
				},
				"Cache-Control": []string{
					"private, max-age=60",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// more-top-stories rec-ai endpoints
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai more-top-stories with encoded url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUveDFUR1RDYlc5eVpTL1VTQVQvYzFkNTZjNGUtZGI4NS0xMWViLTg2YzItMGFhNjYyOGM2ZjNiLzFfMS83NTE0NDAwMDAxfjEyMzEyNH4xMjQxMzQxM343NDc1Mzc0MDAxfjc1MjE3ODgwMDEv",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai more-top-stories with decoded url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTCbW9yZS/USAT/c1d56c4e-db85-11eb-86c2-0aa6628c6f3b/1_1/7514400001~123124~12413413~7475374001~7521788001/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute tag-ids Peng-22249 with decoded url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTCdGFnLQ/USAT/1_1/a173914f-749b-454a-81ed-e37783d9c6f6~b148fcb0-2476-47f7-b49c-bb3815202d69/5/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute tag-ids PENG-22249 with encoded url returns 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUveDFUR1RDZEdGbkxRL1BJTkQvMV8xL2ExNzM5MTRmLTc0OWItNDU0YS04MWVkLWUzNzc4M2Q5YzZmNn5iMTQ4ZmNiMC0yNDc2LTQ3ZjctYjQ5Yy1iYjM4MTUyMDJkNjkvNS8",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute more top stories ssts Peng-22249 with decoded url returns a 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/x1TGTCbW9zcw/USAT/c1d56c4e-db85-11eb-86c2-0aa6628c6f3b/1_1/5/optional~ids/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Status{
			Request: fastly.Request{
				Description: "rec-ai-compute more top stories tags PENG-22249 with encoded url returns 200",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/eUdPVnUveDFUR1RDYlc5MFlRL1VTQVQvYzFkNTZjNGUtZGI4NS0xMWViLTg2YzItMGFhNjYyOGM2ZjNiLzFfMS81L29wdGlvbmFsfmlkcy8=",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
	}

	for _, test := range tests {
		if false {
			test.Execute(t)
		}
	}
}

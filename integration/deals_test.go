package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestDeals(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// coupons deals endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "gets a 200 status along with proper headers",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/deals",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"DEALS backend",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		// widget redirect
		fastly.Header{
			Request: fastly.Request{
				Description: "gets a 301 status along with for old widget urls",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/money/coupons/widget/target",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusMovedPermanently,
			Headers: http.Header{
				"Location": []string{
					"/deals/widget/target",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}

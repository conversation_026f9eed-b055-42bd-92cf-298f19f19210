package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestPetsHub(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		// petshub endpoints
		fastly.Header{
			Request: fastly.Request{
				Description: "petshub gets a 200 status",
				Method:      "GET",
				Scheme:      "https://",
				Host:        "www.usatoday.com",
				Path:        "/pets-animals/dog",
				UA:          UserAgentMacOSChrome,
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path": []string{
					"petshub backend:set",
				},
				"Surrogate-Key": []string{
					"petshub",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}

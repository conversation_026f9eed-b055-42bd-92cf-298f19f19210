/**
    Use when we need to allow Bo<PERSON> using old device/browser versions to receive the Tangent page,
    like when we serve the Unsupported Browser page because there is no UW or CP fallback option.
*/
sub check_tangent_supported_browser_device_allow_old_bots {
  if (req.http.X-UA-Device == "bot") {
    set req.http.Gannett-Custom:tng-supported = "1";
  }
}

/**
  Drives browser / detection logic for adaptive pages on Tangent.
*/
sub check_tangent_supported_browser_device_adaptive {
  if (
    req.http.Sec-CH-UA-Mobile == "?1" ||
    req.http.Gannett-Browser:Variant == "mobile" # not all mobile variants will have this set
  ) {
    call shared_tangent_set_mobile;
  }

  if (
    req.http.Gannett-OS:Name == "macos" ||
    # fastly device detection vars are deprecated - migrating over to custom Gannett detection
    client.os.name == "Mac" || # new value as of 12/3
    client.os.name == "OS X")
  {
    # extract OS version in both formats e.g. 10.12 and 10_13
    if (req.http.Gannett-OS:Version-Major && req.http.Gannett-OS:Version-Minor) {
      if (std.atoi(req.http.Gannett-OS:Version-Major) > 10 || std.atoi(req.http.Gannett-OS:Version-Major) == 10 && std.atoi(req.http.Gannett-OS:Version-Minor) > 12) {
        call check_tangent_supported_browser_versions;
      }
    # fastly device detection vars are deprecated - will phase out
    } elseif (client.os.version ~ "(\d+)(?:\.|_)(\d+)") {
      if (std.atoi(re.group.1) > 10 || std.atoi(re.group.1) == 10 && std.atoi(re.group.2) > 12) {
        call check_tangent_supported_browser_versions;
      }
    } else {
      call check_tangent_supported_browser_versions;
    }
  } elseif (
    req.http.Gannett-OS:Name == "windows" ||
    client.os.name ~ "Windows" # deprecated
  ) {
    if (req.http.Gannett-OS:Version-Major) {
      if (std.atoi(req.http.Gannett-OS:Version-Major) > 9) {
        call check_tangent_supported_browser_versions;
      }
    # fastly device detection vars are deprecated - will phase out
    } elseif (req.http.user-agent ~ "NT\s(\d+)\.\d+") {
      if (std.atoi(re.group.1) > 9) {
        call check_tangent_supported_browser_versions;
      }
    } else {
      call check_tangent_supported_browser_versions;
    }
  } elseif (
    req.http.Gannett-OS:Name == "iphone" ||
    req.http.Gannett-OS:Name == "ipad" ||
    table.lookup(shared_tangent_apple_os_non_desktop, client.os.name) # fastly device detection vars are deprecated - will phase out
  ) {
    if (
      std.atoi(req.http.Gannett-OS:Version-Major) == 15 && std.atoi(req.http.Gannett-OS:Version-Minor) > 5 ||
      std.atoi(req.http.Gannett-OS:Version-Major) > 15
    ) {
      if (!client.platform.tablet) {
        call set_tangent_mobile;
      }
      set req.http.Gannett-Custom:tng-supported = "1";
    }
  } elseif (
    req.http.Gannett-OS:Name == "android" ||
    client.os.name == "Android" # fastly device detection vars are deprecated - will phase out
  ) {
    if (!client.platform.tablet) {
      call set_tangent_mobile;
    }
    call check_tangent_supported_browser_versions;
  } elseif (req.http.Gannett-OS:Name == "linux") {
    if (
      client.platform.mobile && req.http.user-agent !~ "x86_64" ||
      req.http.User-Agent ~ "(?i)Chrome Mobile"
    ) {
      call set_tangent_mobile;
    }
    call check_tangent_supported_browser_versions;
  } elseif (
    !client.platform.mobile &&
    client.platform.vendor != "BlackBerry"
  ) {
    if (req.http.User-Agent ~ "(?i)Chrome Mobile") {
      call set_tangent_mobile;
    }
    call check_tangent_supported_browser_versions;
  }
  # run at the end to ensure mobile detection rules are executed
  if (querystring.get(req.url, "gnt-tangent") == "1") {
    set req.http.Gannett-Custom:tng-supported = "1";
  }
}
/**
  Drives browser / detection logic for adaptive pages on Tangent.
*/
sub check_tangent_supported_browser_device_adaptive_no_bot {
  if (req.http.X-UA-Device != "bot") {
    call check_tangent_supported_browser_device_adaptive;
  }
}
/**
  Drives browser / detection logic for responsive pages on Tangent.
*/
sub  check_tangent_supported_browser_device_desktop_no_bot {
  if (req.http.X-UA-Device != "bot") {
    if (
        (req.http.Gannett-OS:Name == "ipad" && (std.atoi(req.http.Gannett-OS:Version-Major) == 15 && std.atoi(req.http.Gannett-OS:Version-Minor) > 5 || std.atoi(req.http.Gannett-OS:Version-Major) > 15)) ||
        req.http.X-UA-Device == "desktop" && req.http.X-UA-Vendor != "blackberry" && req.http.X-UA-Vendor != "apple" ||
        (req.http.Gannett-OS:Name == "android" || client.os.name == "Android") && client.platform.tablet
    ) {
      call check_tangent_supported_browser_versions;
    }
  }
}

sub check_tangent_supported_browser_versions {
  set req.http.Gannett-Custom:tng-supported = "0";
  if (!(req.http.User-Agent ~ "(?i)(MSIE|Trident|Opera Mini|Presto|KAIOS|Tizen)")) {
    if (req.http.Gannett-Browser:Name != "edge") {
      if (!(req.http.User-Agent ~ "(?i)firefox/([\d]+)\." && std.atoi(re.group.1) < 115)) {
        if (!(req.http.User-Agent ~ "(?i)goanna/([\d]*)\.([\d]*)" && (std.atoi(re.group.1) < 6 || std.atoi(re.group.1) == 6 && std.atoi(re.group.2) < 7))) {
          if (!(req.http.User-Agent ~ "(?i)palemoon/([\d]*)\.([\d]*)" && (std.atoi(re.group.1) < 33 || std.atoi(re.group.1) == 33 && std.atoi(re.group.2) < 6))) {
            if (
              !(req.http.Gannett-Browser:Name == "safari" && ((std.atoi(req.http.Gannett-Browser:Version-Major) == 15 && std.atoi(req.http.Gannett-Browser:Version-Minor) < 6) || std.atoi(req.http.Gannett-Browser:Version-Major) < 15)) ||
              (req.http.Gannett-OS:Name == "macos" && req.http.User-Agent ~ "(?i)(?:crios|edgios)/([\d]+)" && std.atoi(re.group.1) >= 126)
            ) {
              if (!(req.http.User-Agent ~ "(?i)chrome\/([\d]+)\." && std.atoi(re.group.1) < 108)) { # for Chrome and other chromium based browsers
                set req.http.Gannett-Custom:tng-supported = "1";
                # check/set earlyhints & link preload
                call shared_helpers_frontend_check_early_hints_preload_support;
                call shared_helpers_frontend_usat_uscp_set_earlyhints_gup_user_data;
              }
            }
          }
        }
      }
    }
  }
}

sub process_tangent_rules {
  # redirect unintended inline roadblock qsp for not USAT or signed-in premium user
  if (
    req.url.qs ~ "gnt-cfr" &&
    req.http.Cookie:gup_lng ~ {"hma%22%3A%20true"}
  ) {
    set req.http.x-Redir-Url = "https://" req.http.host req.url.path;
    error 702 req.http.x-Redir-Url;
  }
  if (req.url.qs !~ "gnt-tangent=0") {
    if (
      req.url.path ~ "^/story" ||
      (
        req.url.path ~ "^/videos/" &&
        req.url.path != "/videos/"
      ) ||
      req.url.path ~ "^/picture-gallery/" ||
      req.url.path ~ "^/in-depth/" || # legacy content forced on tangent
      req.url.path ~ "^/mosaic-story/" # legacy content forced on tangent
    ) {
      call tangent_asset;
    } elseif (req.url.path ~ "^/live-story/") {
      call shared_tangent_live_story;
    } elseif (req.url.path ~ "^/search/?") {
      call tangent_search;
    } elseif (req.url.path ~ "^/tangsvc/") {
      call shared_tangent_tangsvc;
    } elseif (req.url.path ~ "^/tangfrag/") {
      call tangent_tangfrag;
      if (req.url.path ~ "/sports/gaming/") {
        call tangent_sportsgaming_geo;
      }
    } elseif (
      req.url.path == "/favicon.ico" ||
      req.url.path ~ "-q1a2z3.+\.json$" ||
      req.url.path ~ "^/sitelogos/" ||
      req.url.path ~ "^/(?:apple-)?touch-icon(?:-\d+x\d+)?(?:-precomposed)?.png" ||
      req.url.path == "/.well-known/assetlinks.json" ||
      req.url.path ~ "^(/.well-known)?/apple-app-site-association$" ||
      req.url.path == "/ads.txt" ||
      req.url.path == "/app-ads.txt" ||
      req.url.path == "/robots.txt"
    ) {
      call tangent_set_x_origin_expected_host;
      call keep_origin_cache_control;
      #disable CAM for request
      call set_cam_header_to_disabled;
    } elseif (req.url.path ~ "^/tangstatic") {
      set req.http.Gannett-Custom:disable-vary-cache = "true";
      call tangent_set_x_origin_expected_host;
      call keep_origin_cache_control;
      #disable CAM for request
      call set_cam_header_to_disabled;
    } elseif (req.url.path ~ "^/.cam-tangent/") {
      if (req.url.path ~ "^/.cam-tangent/asset/\d+-[a-z]+\.(json|html)$") {
        call tangent_set_x_origin_expected_host;
      } else {
        error 800;
      }
    } elseif (req.url.path == "/tangfrag-metadata.json") {
      if (table.lookup(access_keys, req.http.Authorization, "NOTFOUND") == "NOTFOUND") {
        error 971;
      }
      call tangent_fragments_set_x_origin_expected_host;
      call keep_origin_cache_control;
      #disable CAM for request
      call set_cam_header_to_disabled;
    } elseif (req.url.path == "/sw.js" || req.url.path == "/push-worker.js") {
      call tangent_set_x_origin_expected_host;
      #disable CAM for request
      call set_cam_header_to_disabled;
    } elseif (req.url.path ~ "^/interactives/sponsor-story/") {
      call tangent_interactives_sponsor_story;
    } elseif (req.url.path ~ "^/marketplace/(cars|jobs)") {
      call tangent_marketplace;
    } elseif (req.url.path ~ "^/errors/") {
      call tangent_error_pages;
    } elseif (req.url.path ~ "^/sitemap/") {
      call shared_tangent_process_sitemaps;
    } elseif (req.url.path ~ "^/staff/") {
      call shared_tangent_staff_pages;
    } else {
      if (req.http.Gannett-Custom:site-name == "usatoday") {
        call tangent_recalls;
      }
      call tangent_section_front;
    }

    if (req.http.x-origin-expected-host ~ "^tangent\.") {
      call tangent_sportsplus_headlines;
    }
  }
}

sub select_tangent_backend {
  if(randombool(std.atoi(table.lookup(weight_shift, "tangent")), 100)) {
    if (req.http.x-origin-expected-host ~ "^tangent-fragments") {
      set req.http.Gannett-Debug-Path-Item = "region: tangent-fragments_" req.http.Gannett-Custom:site-name " east";
      set req.backend = F_tangent_fragments_east;
    } else {
      set req.http.Gannett-Debug-Path-Item = "region: tangent_" req.http.Gannett-Custom:site-name " east";
      set req.backend = F_tangent_east;
    }
    call shared_helpers_general_record_object_path;
  } else {
    if (req.http.x-origin-expected-host ~ "^tangent-fragments") {
      set req.http.Gannett-Debug-Path-Item = "region: tangent-fragments_" req.http.Gannett-Custom:site-name " west";
      set req.backend = F_tangent_fragments_west;
    } else {
      set req.http.Gannett-Debug-Path-Item = "region: tangent_" req.http.Gannett-Custom:site-name " west";
      set req.backend = F_tangent_west;
    }
    call shared_helpers_general_record_object_path;
  }
  # check that the backend is healthy
  if(req.backend == F_tangent_east && !req.backend.healthy) {
    set req.backend = F_tangent_west;
    set req.http.Gannett-Debug-Path-Item = "tangent_" req.http.Gannett-Custom:site-name " east unhealthy";
    call shared_helpers_general_record_object_path;
  } else if(req.backend == F_tangent_west && !req.backend.healthy) {
    set req.backend = F_tangent_east;
    set req.http.Gannett-Debug-Path-Item = "tangent_" req.http.Gannett-Custom:site-name " west unhealthy";
    call shared_helpers_general_record_object_path;
  } else if(req.backend == F_tangent_fragments_east && !req.backend.healthy) {
    set req.backend = F_tangent_fragments_west;
    set req.http.Gannett-Debug-Path-Item = "tangent-fragments_" req.http.Gannett-Custom:site-name " east unhealthy";
    call shared_helpers_general_record_object_path;
  } else if(req.backend == F_tangent_fragments_west && !req.backend.healthy) {
    set req.backend = F_tangent_fragments_east;
    set req.http.Gannett-Debug-Path-Item = "tangent-fragments_" req.http.Gannett-Custom:site-name " west unhealthy";
    call shared_helpers_general_record_object_path;
  }
}

sub set_tangent_mobile {
  set req.http.gnt-mobile = "1";
  set req.http.Gannett-Debug-Path-Item = "gnt-mobile";
  call shared_helpers_general_record_object_path;
}

sub tangent_restrict_safeframe_domain {
  if (req.http.host ~ "usatodaynetworkservice\.com$") {
    if (req.url.path !~ "^/tangstatic" &&
        req.url.path != "/favicon.ico" &&
        req.url.path !~ "^/(?:apple-)?touch-icon(?:-\d+x\d+)?(?:-precomposed)?.png"
    ) {
      error 804;
    }
  }
}

# for asset types enabled on tangent
sub tangent_asset {
  call tangent_redirect_malformed_urls;
  call redirect_on_missing_trailing_slash;
  if (req.url.path ~ "/story/draft/") {
    set req.http.Gannett-Debug-Path-Item = "404 story draft";
    call shared_helpers_general_record_object_path;
    set req.url = "/errors/404/";
    call error_page_restart_recv;
  } elseif (req.url.path ~ "/(\d{5,})/$") {
    if (
      table.lookup(tangent_asset_sponsored_blacklist, re.group.1) != "true" &&
      table.lookup(tangent_asset_editorial_blacklist, re.group.1) != "true"
    ) {
      if (
        req.url.path !~ "/isbn/" &&
        req.url.path !~ "^/story/community-hub/funeral-planning" &&
        req.url.path !~ "^/videos/0/0/0" &&
        req.url.path !~ "^/videos/embed"
      ) {
        call check_tangent_supported_browser_device_adaptive;
        call check_tangent_supported_browser_device_allow_old_bots;
        if (req.http.Gannett-Custom:tng-supported == "1") {
          call tangent_set_x_origin_expected_host;
          call set_weather_data;
          call tangent_set_banner;
        }
      }
    }
  } else {
    set req.http.Gannett-Debug-Path-Item = "404 invalid path";
    call shared_helpers_general_record_object_path;

    set req.url = "/errors/404/";
    call error_page_restart_recv;
  }
}

sub tangent_section_front {
  if (req.url.ext == "") {
    set req.http.Gannett-Custom:tng-front = "0";
    # redirect malformed URLs or to trailing slash to reduce unecessary calls to CP / UW origin before tangent section allowlist lookup
    call tangent_redirect_malformed_urls;
    call redirect_on_missing_trailing_slash;
    if (req.http.Gannett-Custom:site-name == "usatoday" || req.http.Gannett-Custom:site-name == "usatodaysportsplus") {
      set req.http.Gannett-Custom:tng-front = "1";
      call check_tangent_supported_browser_device_adaptive;
      if (req.http.Gannett-Custom:tng-supported == "1") {
        call tangent_set_x_origin_expected_host;
        call set_weather_data;
        call tangent_set_banner;
      }
    }
  }
}

sub tangent_search {
  # matches /search & /search/some_search_query & captures "some_search_query" for group, may need to revisit if Tangent supports /search subsections
  if (req.url.path ~ "^/search/?([^?/]*)?") {
    # would match "search_query" from /search/search_query/
    declare local var.search-path-suffix STRING;
    set var.search-path-suffix = re.group.1;

    call check_tangent_supported_browser_device_adaptive_no_bot;
    if (req.http.Gannett-Custom:tng-supported == "1") {
      # when search originated from CP e.g. /search/some_search_query/ reconstruct url for Tangent
      if (req.url.path != "/search/") {
        if (var.search-path-suffix == "" && req.url.qs != "") {
          set req.http.x-Redir-Url = "https://" req.http.host "/search/?" + req.url.qs;
        } elseif (var.search-path-suffix == "" && req.url.qs == "") {
          set req.http.x-Redir-Url = "https://" req.http.host "/search/";
        } else {
          set req.http.x-Redir-Url = "https://" req.http.host "/search/?q=" var.search-path-suffix;
        }
        error 701 req.http.x-Redir-Url;
      # for invalid "page" qsp values outside the range of 1-5
      } elseif (req.url.qs ~ "&page=([^&]*)" || req.url.qs ~ "^page=([^&]*)") {
        if (re.group.1 !~ "^[1-5]$") {
          set req.http.Gannett-Debug-Path-Item = "search error redir";
          call shared_helpers_general_record_object_path;

          set req.url = "/errors/404/";
          call error_page_restart_recv;
        }
      }
      # concierge search redirects
      if (req.url.qs ~ "^q=([^&]+)" || req.url.qs ~ "&q=([^&]+)") {
        declare local var.concierge-search-redirect STRING;
        set var.concierge-search-redirect = table.lookup(tangent_concierge_search_redirect_usat, re.group.1, "false");
        if (var.concierge-search-redirect != "false") {
          set req.http.Gannett-Debug-Path-Item = var.concierge-search-redirect " redir";
          call shared_helpers_general_record_object_path;

          set req.http.x-Redir-Url = var.concierge-search-redirect;
          error 701 req.http.x-Redir-Url;
        }
      }
      call tangent_set_x_origin_expected_host;
      call set_weather_data;
      call tangent_set_banner;
    } else {
      #if unsupported browser redirect to unsupported-browser page
      set req.http.Gannett-Debug-Path-Item = "tangent unsupported browser";
      call shared_helpers_general_record_object_path;
      set req.http.gnt-client:unsupported-browser = "tng";
      error 803 "Unsupported Browser";
    }
  }
}

sub tangent_recalls {
  if (req.url.path ~ "^/recalls/?([\w'-]*)/?([\w'-]*)/?(\S*)") {
    declare local var.category STRING;
    declare local var.article-id STRING;
    declare local var.unsupported-path-param STRING;
    set var.category = re.group.1;
    set var.article-id = re.group.2;
    set var.unsupported-path-param = re.group.3;
    set req.http.x-origin-expected-host = "tangent.usatoday.com";

    call tangent_redirect_malformed_urls;
    call redirect_on_missing_trailing_slash;

    call check_tangent_supported_browser_device_adaptive;
    call check_tangent_supported_browser_device_allow_old_bots;

    if (req.http.Gannett-Custom:tng-supported == "1") {
      # for invalid url paths or invalid categories - /recalls/:category/:articleId/garbage or /recalls/:category-invalid/
      if (var.unsupported-path-param != "" || var.category != "" && table.lookup(tangent_recalls_category_whitelist, var.category, "false") == "false") {
        # redirect unsuppoted routes to 404 page
        set req.http.Gannett-Debug-Path-Item = "recalls 404 redir";
        call shared_helpers_general_record_object_path;

        set req.url = "/errors/404/";
        call error_page_restart_recv;

      # for /recalls/:category/:articleId/
      } elseif (var.article-id != "") {
        # unset q and p query params so they don't create additional cache entries for these urls as they will be whitelisted for /recalls in vcl_hash
        set req.url = querystring.regfilter(req.url, "q|page");
        set req.http.Gannett-Debug-Path-Item = "recalls unset qsp";
        call shared_helpers_general_record_object_path;
      # for /recalls/:category/ or /recalls/
      } else {
        # unset page qsp if q qsp is not present
        if (var.category == "" && req.url.qs !~ "&q=" && req.url.qs !~ "^q=") {
          # unset 'page' parameter
          set req.url = querystring.regfilter(req.url, "page");
          set req.http.Gannett-Debug-Path-Item = "recalls unset qsp";
          call shared_helpers_general_record_object_path;
        }
        # validate page range
        if (req.url.qs ~ "&page=([^&]*)" || req.url.qs ~ "^page=([^&]*)") {
          if (re.group.1 !~ "^[1-5]$") {
            set req.http.Gannett-Debug-Path-Item = "recalls page redir";
            call shared_helpers_general_record_object_path;

            set req.url = "/errors/404/";
            call error_page_restart_recv;
          }
        }
      }
    } else {
      #if unsupported browser redirect to unsupported-browser page
      set req.http.Gannett-Debug-Path-Item = "tangent unsupported browser";
      call shared_helpers_general_record_object_path;
      set req.http.gnt-client:unsupported-browser = "tng";
      error 803 "Unsupported Browser";
    }
  }
}

# Redesigned Nav on UW (PENG-13480)
sub tangent_nav_on_uw {
  if (req.http.Gannett-Custom:UW == "1") {
    call check_tangent_supported_browser_device_desktop_no_bot;
    if (req.http.Gannett-Custom:tng-supported == "1" ) {
      set req.http.X-NavReskin = "true";
      set req.http.Gannett-Debug-Path-Item = "navreskin";
      call shared_helpers_general_record_object_path;
    }
  }
}

# set weather data to be used in gnt_w cookie
# format: [f~]temperature~condition#~svghash~condition - where f~ is optional weather fallback indicator
# ex1: 40~7-q1a2z3371d08dc~Cloudy
# ex2: f~40~7-q1a2z3371d08dc~Cloudy
# note: must only urlencode specific data components that need it e.g. weather condition text "Mostly Cloudy", in order to keep "*" as is
sub set_weather_data {
  if (
    req.http.gnt-mobile != "1" &&
    req.http.Gannett-Custom:site-name == "usatoday"
  ) {
    declare local var.cookie-data STRING;
    declare local var.weather-data STRING;
    declare local var.zip-prefix STRING;
    declare local var.zip-suffix STRING;
    set var.zip-prefix = substr(client.geo.postal_code, 0, 3);
    set var.zip-suffix = substr(client.geo.postal_code, 3, 2) "=";
    set var.weather-data = if (table.lookup(tangent_supported_country_zip, client.geo.country_code), table.lookup(gnt_weather_data, var.zip-prefix), "");

    # fallback to McLean, VA = 22102
    if (var.weather-data == "") {
      set var.zip-prefix = "221";
      set var.zip-suffix = "02=";
      set var.weather-data = table.lookup(gnt_weather_data, var.zip-prefix);
      set var.cookie-data = "f~"; #indicate fallback used
    }

    if (var.weather-data != "") {
      # extract zip weather data
      set var.weather-data = std.strstr(var.weather-data, var.zip-suffix);
      set var.weather-data = std.replace_suffix(var.weather-data, std.strstr(var.weather-data, "|"), "");
      set var.weather-data = std.replace_prefix(var.weather-data, var.zip-suffix, "");
      # split temp/condition_index pair
      if (var.weather-data ~ "(-?\d+),(\d+)") {
        # re.group.1 = temp , re.group.2 = condition
        set var.weather-data = "f" re.group.2; # for temp lookup

        declare local var.weather-svg STRING;
        set var.weather-svg = table.lookup(gnt_weather_conditions_usat, var.weather-data, "");

        set var.cookie-data = var.cookie-data re.group.1 "~" var.weather-svg "~" urlencode(table.lookup(gnt_weather_conditions_usat, re.group.2, ""));

        set req.http.Gannett-Debug-Path-Item = "weather:" var.cookie-data;
        call shared_helpers_general_record_object_path;

        # don't set cookie later if unchanged
        if (req.http.Cookie:gnt_w != var.cookie-data) {
          set req.http.Gannett-Custom:gnt-w = var.cookie-data;
        }

        # early hints
        if (std.strlen(var.weather-svg) > 0) {
          set req.http.Gannett-Custom:w_svg = var.weather-svg;
          if (req.http.Gannett-Custom:earlyhints) {
            h2.early_hints("link: <https://www.usatoday.com/tangstatic/svg/weather/" var.weather-svg ".svg>;rel=preload;as=image;nopush");
          }
        }
      }
    }
  }
}

sub set_weather_resp_link {
  if (
    req.http.Gannett-Custom:preload &&
    std.strlen(req.http.Gannett-Custom:w_svg) > 0
  ) {
    set resp.http.link = if (resp.http.link, resp.http.link ",", "") "<https://www.usatoday.com/tangstatic/svg/weather/" req.http.Gannett-Custom:w_svg ".svg>;rel=preload;as=image;nopush";
  }
}

# set header with banner data if present for qualifying pages
sub tangent_set_banner {
  if (!req.http.gnt-ba) {
    declare local var.banner STRING;
    set var.banner = table.lookup(gnt_banner_data, req.http.Gannett-Custom:site-name);
    if (std.strlen(var.banner) > 0) {
      set req.http.gnt-ba = var.banner;
    }
  }
}

sub tangent_interactives_sponsor_story {
  call check_tangent_supported_browser_device_adaptive;
  if (req.http.Gannett-Custom:tng-supported == "1") {
    call redirect_on_missing_trailing_slash;
    call tangent_set_x_origin_expected_host;
    if (req.http.Gannett-Custom:earlyhints) {
      h2.early_hints("link: <https://view.ceros.com/scroll-proxy.min.js>;rel=preload;as=script;nopush");
    }
    #disable CAM for request
    call set_cam_header_to_disabled;
  } else {
    set req.http.Gannett-Debug-Path-Item = "tangent unsupported browser";
    call shared_helpers_general_record_object_path;
    set req.http.gnt-client:unsupported-browser = "tng";
    error 803 "Unsupported Browser";
  }
}

sub tangent_tangfrag {
  call check_tangent_supported_browser_device_adaptive;
  if (req.http.Gannett-Custom:tng-supported == "1") {
    call tangent_fragments_set_x_origin_expected_host;
    #disable CAM for request
    call set_cam_header_to_disabled;
  } else {
    set req.http.Gannett-Debug-Path-Item = "tangfrag unsupported browser";
    call shared_helpers_general_record_object_path;
    set req.http.gnt-client:unsupported-browser = "1";
    error 803 "Unsupported Browser";
  }
  if (req.url.path ~ "^/tangfrag/" && req.url.path !~ "/$" ){
    set req.http.x-Redir-Url = req.url.path "/" if (req.url.qs == "", "", "?" req.url.qs);
    if (req.http.Gannett-Debug) {
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path "; tangfrag slash:" req.url.path;
    }
    error 701 req.http.x-Redir-Url;
  }
}

sub redirect_on_missing_trailing_slash {
  if (
    req.http.host ~ "www" && # don't run on other subdomains
    req.url.path !~ "/$" &&
    req.url.path !~ "^/firefly" &&  # CP Firefly Testing Assets
    req.url.path !~ "^/cam/asset" &&  # UW Firefly Testing Assets
    req.url.path !~ "apple-app-site-association$") {
    set req.http.Gannett-Debug-Path-Item = "trailing slash redirect";
    call shared_helpers_general_record_object_path;

    set req.http.x-Redir-Url = "https://" req.http.host req.url.path "/";
    if (req.url.qs != "") {
      set req.http.x-Redir-Url = req.http.x-Redir-Url "?" req.url.qs;
    }
    error 701 req.http.x-Redir-Url;
  }
}

# tangent specific URL validation
sub tangent_redirect_malformed_urls {
  declare local var.path-to-validate STRING;
  set var.path-to-validate = if (req.url.ext == "", req.url.path, req.url.dirname); # to ensure we capture the full path minus the file, as dirname will be truncated if no trailing slash present
  if (
    var.path-to-validate ~ "=" ||
    var.path-to-validate ~ "/wp-includes/" ||
    var.path-to-validate ~ {"%E2%80%9D|%E2%80%9C"}
  ) {
    set req.http.Gannett-Debug-Path-Item = "404 invalid path";
    call shared_helpers_general_record_object_path;

    set req.url = "/errors/404/";
    call error_page_restart_recv;
  }
}

# Tangent overrides at edge pop
sub tangent_deliver_edge_overrides {
  if (
    req.http.x-origin-expected-host ~ "^tangent\." &&
    fastly.ff.visits_this_service == 0
  ) {
    # inline roadblock
    if (
      req.url.qs ~ "gnt-cfr" &&
      resp.http.x-content-access-type != "Premium-Only"
    ) { # redirect away from inline roadblock page if non-premium content (for any tangent site)
      set resp.http.Location = "https://" req.http.host req.url.path;
      set resp.status = 302;
    }
    # must be done before wall-ly deliver
    if (
      req.url.path ~ "^/story/" &&
      resp.http.x-content-access-type == "Premium-Only"
    ) {
      if (req.url.qs !~ "gnt-cfr") { # override redirect url from legacy origin roadblock to inline roadblock
        set resp.http.x-content-restricted-url = req.url.path "?gnt-cfr=1&gca-cat=p"; # hardcoding gca-cat to premium sinces it's the only support cam type for inline roadblocks
      } else { # when inline roadblock prevent redirect
        unset resp.http.x-content-restricted-url;
        set resp.http.Gannett-CAM-Content-Restrict-At = "Origin";
      }
    }
  }
}

# determine sportsplus headlines mode
sub tangent_sportsplus_headlines {
  if (
    req.http.Gannett-Custom:site-name == "usatodaysportsplus" &&
    req.url.path == "/"
  ) {
    if (
      req.http.Cookie:gnt_pt ~ "~" &&
      req.http.Cookie:gup_lng ~ {"auth%22%3A%20true"}
    ){
      # personalized
      set req.http.gnt-sph = "p";
      set req.http.Gannett-Debug-Path-Item = "gnt-sph=p";
      call shared_helpers_general_record_object_path;
    }
  }
}

sub tangent_sportsgaming_geo {
  if (table.lookup(sports_gaming_geo,client.geo.region)) {
    set req.http.gnt-gm-im = "1";
    set req.http.Gannett-Debug-Path-Item = "sports-gaming-in-market:" client.geo.region;
  } else {
    set req.http.Gannett-Debug-Path-Item = "sports-gaming-out-market:"client.geo.region;
  }
  call shared_helpers_general_record_object_path;
}

sub tangent_marketplace {
  call check_tangent_supported_browser_device_adaptive;
  if (req.http.Gannett-Custom:tng-supported == "1") {
    call redirect_on_missing_trailing_slash;
    call tangent_set_x_origin_expected_host;
    #disable CAM for request
    call set_cam_header_to_disabled;
  } else {
    set req.http.Gannett-Debug-Path-Item = "tangent unsupported browser";
    call shared_helpers_general_record_object_path;
    set req.http.gnt-client:unsupported-browser = "1";
    error 803 "Unsupported Browser";
  }
}

sub tangent_error_pages {
  call redirect_on_missing_trailing_slash;
  if (req.url.path ~ "^/errors/(404|500)/$") {
    call check_tangent_supported_browser_device_adaptive;
    if (req.http.Gannett-Custom:tng-supported == "1") {
      call tangent_set_x_origin_expected_host;
      call set_weather_data;
      call tangent_set_banner;
    } else {
      call uw_set_x_origin_expected_host;
    }
    #disable CAM for request
    call set_cam_header_to_disabled;
  } else {
    set req.url = "/errors/404/";
    call error_page_restart_recv;
  }
}

table tangent_supported_country_zip {
  "AS": "1",
  "FM": "1",
  "GU": "1",
  "MH": "1",
  "MP": "1",
  "PR": "1",
  "PW": "1",
  "US": "1",
  "VI": "1"
}

sub tangent_set_x_origin_expected_host {
  set req.http.x-origin-expected-host = "tangent." req.http.Gannett-Custom:site-apexdomain;
}

sub tangent_fragments_set_x_origin_expected_host {
  set req.http.x-origin-expected-host = "tangent-fragments." req.http.Gannett-Custom:site-apexdomain;
}

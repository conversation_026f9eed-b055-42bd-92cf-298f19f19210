# paas-vcl-www.usatoday.com
vcl configuration for the following fastly services

1.  ci [`5eNYgHpbgS2XBK6ynQi3wW`](https://manage.fastly.com/configure/services/5eNYgHpbgS2XBK6ynQi3wW) - CI service for PR's pointing to production origin
2.  origin-staging-1 [`CWvH76K9MWV7jQwl4DKLo3`](https://manage.fastly.com/configure/services/CWvH76K9MWV7jQwl4DKLo3) - Staging service that points to staging origin 1
3.  origin-staging-2 [`l3NXk9w7F19zaIzynxMTd7`](https://manage.fastly.com/configure/services/l3NXk9w7F19zaIzynxMTd7) - Staging service that points to staging origin 2
4.  origin-staging-3 [`A2OxOHiOyNZOgY4wDeKSC5`](https://manage.fastly.com/configure/services/A2OxOHiOyNZOgY4wDeKSC5) - Staging service that points to staging origin 3
5.  origin-staging-4 [`fBWItYbfv4gpsdGWnxM3b3`](https://manage.fastly.com/configure/services/fBWItYbfv4gpsdGWnxM3b3) - Staging service that points to staging origin 4
6.  origin-staging [`1Jgkyg8zRYMyWKx0cp4iBy`](https://manage.fastly.com/configure/services/1Jgkyg8zRYMyWKx0cp4iBy) - Staging service that points to staging origin
7.  staging [`7Rat8NP47vw8TaDGrs9A9A`](https://manage.fastly.com/configure/services/7Rat8NP47vw8TaDGrs9A9A) - Staging service that points to production origin
8.  production [`7gc7d4saxzvYNJR6ex1WLe`](https://manage.fastly.com/configure/services/7gc7d4saxzvYNJR6ex1WLe) - Production service pointing to production origin

### Fastly Observability for the Usatoday services:

1.  [`Production www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/7gc7d4saxzvYNJR6ex1WLe?range=1hr) -
2.  [`ci-www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/5eNYgHpbgS2XBK6ynQi3wW?range=1hr) -
3.  [`origin-staging-1-www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/CWvH76K9MWV7jQwl4DKLo3?range=1hr) -
4.  [`origin-staging-2-www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/l3NXk9w7F19zaIzynxMTd7?range=1hr) -
5.  [`origin-staging-3-www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/A2OxOHiOyNZOgY4wDeKSC5?range=1hr) -
6.  [`origin-staging-4-www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/fBWItYbfv4gpsdGWnxM3b3?range=1hr) -
7.  [`origin-staging-www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/1Jgkyg8zRYMyWKx0cp4iBy?range=1hr) -
8.  [`staging-www.usatoday.com Observability`](https://manage.fastly.com/observability/dashboard/system/overview/historic/7Rat8NP47vw8TaDGrs9A9A?range=1hr) -

###  Backend Monitor:

[`Backend Status Monitor`](https://fastly-backends.gannettdigital.com/usat/backend-status/)

### GCP logs

To access the logs and monitors in GCP log in via Okta and navigate to gannett-sre-monitoring

https://console.cloud.google.com/bigquery?project=gannett-sre-monitoring&ws=!1m0


## Standards for Contributing

This is a heavy-traffic repository. While we try to respond to pull requests as quickly as possible, due to the volume we have a 24 hour SLA for any incoming simple redirect requests to review or merge a PR. In order for us to honor this SLA, we ask contributors do a couple things:
* Make a ticket for any pull request that you want merged. Without a ticket, we can't track the time it takes to review, iterate, and deploy changes. The body of the ticket should look something like this:
`I need X PR reviewed, merged, and in production by Y date.` Where Y is at least 24 hours away from ticket creation date.
* For complex logic requests that you don't intend to PR yourself (ie. more than a simple redirect), please make a ticket early enough for us to add it to our next sprint. We cannot honor our 24 hour SLA for these requests.
* Post to #ask-platform-eng slack's channel using the **request help** workflow, assign the appropiate severity and fastly as component. Add the PR link or the ticket (which in return has a link to the PR). Lastly, use SRE or SRE1 reactions to notify the team (although it is not mandatory).
* Do not directly ping maintainers of the repository to get your pull requests prioritized. Instead, post in #ask-platform-eng with the appropiate severity.

## Verifying submodules and libraries are correctly up to date

Libraries in the `/lib` directory are maintained as submodules.  Verify in your git diff that your changes
affect the submodule in the intended way.  If the diff shows a change to a submodule you did not intend, you
can resync it and bring your library path up to date using:
`git submodule update --init`

## Testing

Tests are in `integration/` and grouped by feature or path being tested. If test tables have a dozen or more tests, consider splitting tests into multiple test files.

Utility helpers and content check constants are in `integration/utils*.go` files. Constants have been set for:

* User agents with naming pattern `UserAgent{OS/DEVICE}{BROWSER}`
* Web page strings to match for content on UW, UX, and mhigh backends with naming pattern `Content{BACKEND}{NAME}`
* Present cookies when needed for backend selection/hashing

Test methods have been standardized into these methods in `utils.go` and `utils_types.go`:
* `TestHeader` with `TestDataTextCompare` struct input
* `TestStatusCode` with `TestDataTextCompare` struct input
* `TestBody` with `TestDataStatusCode` struct input



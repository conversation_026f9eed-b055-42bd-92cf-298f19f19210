# This VCL file should contain VCL logic that proxies requests to 3rd party vendors. These subroutines get from main.vcl

############################
### Gambling/Betting App ###
############################

sub gnt_betting_recv {
    if (req.url.path ~ "/$") {
      set req.http.Gannett-Debug-Path-Item = "betting slash redirect";
      call shared_helpers_general_record_object_path;
      set req.url = regsub(req.url.path, "/$", "");
      set req.http.x-Redir-Url = "https://" req.http.host req.url.path;
      if (req.url.qs != "") {
        set req.http.x-Redir-Url = req.http.x-Redir-Url "?" req.url.qs;
      }
      error 701 req.http.x-Redir-Url;
    }
    set req.http.x-append-surrogate = "betting";
    set req.http.Gannett-Debug-Path-Item = "betting";
    call shared_helpers_general_record_object_path;
    if (req.url.path ~ "^/betting" || req.url.path ~ "^/betoffer") {
      set req.http.x-origin-expected-host = "www.usatoday-gdcgroup.com";
    }else {
      set req.http.x-origin-expected-host = "usat2.usatoday-gdcgroup.com";
    }
    set req.http.Authorization = "Basic Z2FubmV0dDpVVldja2Q5YWdwaGt6ZWdy";
    if ( req.http.Cookie ) {
      remove req.http.Cookie;
    }
    # keep old setting without force br
    call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_betting_recv_backend {
    set req.http.client-geo-country = client.geo.country_code;
    set req.http.client-geo-region = client.geo.region;
    if (req.url.path ~ "^/betting" || req.url.path ~ "^/betoffer") {
      set req.backend = F_www_usatoday_gdcgroup_com;
      set req.http.Gannett-Debug-Path-Item = "(www-usat) betting backend";
      call shared_helpers_general_record_object_path;
    }else {
      set req.backend = F_usat2_usatoday_gdcgroup_com;
      set req.http.Gannett-Debug-Path-Item = "(usat2) betting backend";
      call shared_helpers_general_record_object_path;
    }
    return(pass);
}

sub gnt_betting_fetch {
    #betoffer no cache
    set beresp.cacheable = false;
    set beresp.ttl = 0s;
    # set security policy headers
    set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none'";
    set beresp.http.Content-Security-Policy-Report-Only = "script-src https: blob: 'unsafe-inline' 'unsafe-eval' 'self';base-uri 'self';report-uri https://reporting-api.gannettinnovation.com;report-to default";
    set beresp.http.X-Content-Type-Options = "nosniff";
    set beresp.http.X-Frame-Options = "deny";
    set beresp.http.X-XSS-Protection = "1; mode=block";
    set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
    set beresp.http.Permissions-Policy = "camera=(),display-capture=(),geolocation=(),microphone=(),payment=(),usb=(),xr-spatial-tracking=()";
    set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
    set beresp.http.Cross-Origin-Resource-Policy = "same-origin";
}

sub gnt_betting_deliver {
    if (!req.http.Gannett-Debug) {
      unset resp.http.aws-region;
      unset resp.http.cf-cache-status;
      unset resp.http.cf-ray;
    }
    if (
      req.http.Gannett-Browser:Name == "safari" ||
      req.http.Gannett-OS:Name == "iphone" ||
      req.http.Gannett-OS:Name == "ipad"
    ) {
      unset resp.http.Cross-Origin-Opener-Policy;
    }
}

##############################
### forbes marketplace App ###
##############################

sub gnt_blueprint_recv {
    if (req.http.host == "origin-staging-2-www.usatoday.com"){
      set req.http.Authorization = "Basic Y21zLXVzZXI6UUE1d1ppV3lGYzFRb3o2ZnRRSHA=";
      set req.http.x-origin-expected-host = "usat-staging.mktplatforms.com";
    } else {
      set req.http.x-origin-expected-host = "usat-prod.mktplatforms.com";
    }

    if (req.http.Gannett-Debug) {
        set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path ";" "blueprint: " req.url " " time.elapsed.msec "ms";
    }

    #trailing slash check and redirect
    if (req.url == "/money/blueprint"){
      set req.http.Gannett-Debug-Path-Item = "trailing slash redirect";
      call shared_helpers_general_record_object_path;
      set req.http.x-Redir-Url = "https://" req.http.host req.url.path "/";
      if (req.url.qs != "") {
        set req.http.x-Redir-Url = req.http.x-Redir-Url "?" req.url.qs;
      }
      error 701 req.http.x-Redir-Url;
    }
    if (req.url.path ~ "^/life/blueprint/l/(.*)") {
      set req.url = "/money/blueprint/l/health/" + re.group.1;
    }
}

sub gnt_blueprint_backend {
    set req.http.Gannett-Debug-Path-Item = req.http.x-origin-expected-host " BLUEPRINT backend";
    call shared_helpers_general_record_object_path;
    set req.backend = F_blueprint_backend;

    # Setting surrogate keys for different resources
      # set default SK for all blueprint requests
    set req.http.x-append-surrogate = "forbes";
      # set url based SK for assets
    if (req.url.ext ~ "(?i)^(gif|png|jpe?g|webp|js|ttf|svg|woff2|wff|css)$") {
      set req.http.x-append-surrogate = querystring.remove(req.url) " " "forbes";
    } # set generic SK for all images
    else if (req.url.path ~ "^/money/blueprint/images/uploads") {
      set req.http.x-append-surrogate = "/money/blueprint/images/uploads" " " "forbes";
    } # set generic SK for all resources

    ## always cache these images & static assets
    if (req.request == "GET" && req.url.ext ~ "(?i)^(gif|png|jpe?g|webp|js|ttf|svg|woff2|wff|css)$") {
      remove req.http.cookie;
      return(lookup);
    }

    if (req.url.path ~ "(wp-admin|wp-login)") {
      return(pass);
    }

    if ( req.http.Cookie ) {
      ### do not cache authenticated sessions
      if (req.http.Cookie ~ "wlmktp_9b58a8779383348cb22aedb790622595") {
        return(pass);
      }
    }
}

sub gnt_vendor_io {
  #Image optimizer request condition for Blueprint and Threeships integrations
  if (req.url.ext ~ "(?i)^(gif|png|jpe?g|webp)$" && (req.url ~ "^/money/blueprint" || req.url ~ "^/money/homefront/")) {
    set req.http.x-fastly-imageopto-api = "fastly";
    set req.http.Gannett-Debug-Path-Item = "IMAGE-OPTIMIZATION WORKNG";
    call shared_helpers_general_record_object_path;
  }
}

sub gnt_blueprint_fetch {
    # preserve cache-control setting
    call keep_origin_cache_control;

      # this endpoint is used by Tangent to get homepage module data
    if (req.url.path ~ "^/money/blueprint/wp-json/v1/feed"){
      set beresp.ttl = 1m;
      # set stale_while_revalidate and stale_if_error for assets
      set beresp.stale_while_revalidate = 1y;
      set beresp.stale_if_error = 1y;
    } # static resources/images
    else if (req.url.path ~ "^/money/blueprint/wp-content" || req.url.path ~ "^/money/blueprint/images/uploads" ) {
      set beresp.ttl = 1y;
      # set stale_while_revalidate and stale_if_error for assets
      set beresp.stale_while_revalidate = 1y;
      set beresp.stale_if_error = 1y;
    }

    if (req.url.path ~ "(wp-admin|wp-login)" ||
    req.http.Cookie ~ "wlmktp_9b58a8779383348cb22aedb790622595") {
        unset beresp.http.etag;
        set beresp.http.Cache-Control = "private,no-cache,no-store";
        set beresp.ttl = 0s;
    }

    # setting client side headers for (non-admin) html/non-html requests
    if (req.url.path !~ "(wp-admin|wp-login)") {
      if (beresp.http.content-type ~ "^(text\/html)") {
        set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none';report-uri https://reporting-api.gannettinnovation.com;report-to default";
        set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
        set beresp.http.Permissions-Policy = "camera=(),display-capture=(),geolocation=(),microphone=(),payment=(),usb=(),xr-spatial-tracking=()";
        set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
        set beresp.http.X-Content-Type-Options = "nosniff";
        if (req.url.path !~ "/money/blueprint/widgets/embed/") {
          set beresp.http.X-Frame-Options = "deny";
        }
        set beresp.http.X-XSS-Protection = "1; mode=block";
      } else {
        set beresp.http.Timing-Allow-Origin = "*";
      }
    }
}

sub gnt_blueprint_deliver {
  if(std.prefixof(resp.http.Content-Type, "text/html")) {
    if (fastly.ff.visits_this_service == 0) {
      if (resp.http.Cache-Control ~ "no-store") {
        set resp.http.Cache-Control = "private,no-store";
      } else {
        set resp.http.Cache-Control = "private,no-cache";
      }
    }
  }
}

##############################
### Booklist App ###
##############################

sub gnt_booklist_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:booklist = "1";
  set req.http.Gannett-Debug-Path-Item = "booklist";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "booklist-us-east1-suh2mjo3oa-ue.a.run.app";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_booklist_backend {
  set req.http.Gannett-Debug-Path-Item = "booklist backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/booklist";
  set req.backend = F_booklist_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_booklist_east;
  } else {
      set req.backend = F_booklist_west;
  }
  # check that the backend is healthy
  if(req.backend == F_booklist_east && !req.backend.healthy) {
      set req.backend = F_booklist_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_booklist_west && !req.backend.healthy) {
      set req.backend = F_booklist_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
  if(req.http.host ~ "^origin-staging-") {
    set req.http.x-origin-expected-host = "booklist-us-east1-7tpppv3ona-ue.a.run.app";
  } else {
    if(req.backend == F_booklist_east) {
    set req.http.x-origin-expected-host = "booklist-us-east1-suh2mjo3oa-ue.a.run.app";
  } else {
    set req.http.x-origin-expected-host = "booklist-us-west1-suh2mjo3oa-uw.a.run.app";
  }
 }
}

sub gnt_booklist_fetch {
  if (beresp.http.Surrogate-Control !~ "max-age"){
      if (beresp.http.Surrogate-Control ~ "no-cache|no-store"){
      return(pass);
    } else {
      set beresp.ttl = 60s;
    }
  } else {
    set beresp.ttl = 60s;
  }
  # Specifically set browser cache rules for fonts
  if (req.url.path ~ "^/booklist/(webfonts|_next)") {
      set beresp.http.Cache-Control = "public, max-age=31536000, immutable";
      set beresp.ttl = 3600s;
      call keep_origin_cache_control;
  }
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
}

sub gnt_booklist_hash {
  # Allow caching for requests to booklist endpoints
      if (req.url.path ~ "^/booklist/api/graphql" ) {
          set req.hash += req.body.base64;
      }
}

##############################
### realestate App ###
##############################

sub gnt_realestate_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:realestate = "1";
  set req.http.Gannett-Debug-Path-Item = "realestate";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "realestate.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_realestate_backend {
  set req.http.Gannett-Debug-Path-Item = "realestate backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/real-estate";
  set req.backend = F_realestate_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_realestate_east;
  } else {
      set req.backend = F_realestate_west;
  }
  # check that the backend is healthy
  if(req.backend == F_realestate_east && !req.backend.healthy) {
      set req.backend = F_realestate_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_realestate_west && !req.backend.healthy) {
      set req.backend = F_realestate_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_realestate_fetch {
  call keep_origin_cache_control;
  if (req.url.path ~ "^/real-estate/(_next|assets|images)"){
    set beresp.ttl = 1y;
  }
}

##############################
### tailgating App ###
##############################

sub gnt_tailgating_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:tailgating = "1";
  set req.http.Gannett-Debug-Path-Item = "tailgating";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "tailgating.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_tailgating_backend {
  set req.http.Gannett-Debug-Path-Item = "tailgating backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/great-american-tailgate";
  set req.backend = F_tailgating_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_tailgating_east;
  } else {
      set req.backend = F_tailgating_west;
  }
  # check that the backend is healthy
  if(req.backend == F_tailgating_east && !req.backend.healthy) {
      set req.backend = F_tailgating_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_tailgating_west && !req.backend.healthy) {
      set req.backend = F_tailgating_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_tailgating_fetch {
  set beresp.ttl = 3600s;
  call keep_origin_cache_control;
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
}

##############################
### advertise-with-us App ###
##############################

sub gnt_advertise_with_us_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:advertisewithus = "1";
  set req.http.Gannett-Debug-Path-Item = "advertise-with-us";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "advertise-with-us.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_advertise_with_us_backend {
  set req.http.Gannett-Debug-Path-Item = "advertise-with-us backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/advertise-with-us";
  set req.backend = F_advertise_with_us_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_advertise_with_us_east;
  } else {
      set req.backend = F_advertise_with_us_west;
  }
  # check that the backend is healthy
  if(req.backend == F_advertise_with_us_east && !req.backend.healthy) {
      set req.backend = F_advertise_with_us_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_advertise_with_us_west && !req.backend.healthy) {
      set req.backend = F_advertise_with_us_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_advertise_with_us_fetch {
  set beresp.ttl = 3600s;
  if (req.url.path ~ "^/group-subscriptions-(corporate|education)/_next"){
    set beresp.ttl = 30d;
  }
  call keep_origin_cache_control;
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
}

##############################
### vacation App ###
##############################

sub gnt_vacation_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:vacation = "1";
  set req.http.Gannett-Debug-Path-Item = "vacation";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "vacation.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_vacation_backend {
  set req.http.Gannett-Debug-Path-Item = "vacation backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/great-american-vacation";
  set req.backend = F_vacation_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_vacation_east;
  } else {
      set req.backend = F_vacation_west;
  }
  # check that the backend is healthy
  if(req.backend == F_vacation_east && !req.backend.healthy) {
      set req.backend = F_vacation_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_vacation_west && !req.backend.healthy) {
      set req.backend = F_vacation_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_vacation_fetch {
  set beresp.ttl = 3600s;
  call keep_origin_cache_control;
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
}

##############################
### woty App ###
##############################

sub gnt_woty_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:woty = "1";
  set req.http.Gannett-Debug-Path-Item = "woty";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "woty.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_woty_backend {
  set req.http.Gannett-Debug-Path-Item = "woty backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/women-of-the-year-2024";
  set req.backend = F_woty_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_woty_east;
  } else {
      set req.backend = F_woty_west;
  }
  # check that the backend is healthy
  if(req.backend == F_woty_east && !req.backend.healthy) {
      set req.backend = F_woty_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_woty_west && !req.backend.healthy) {
      set req.backend = F_woty_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_woty_fetch {
  call keep_origin_cache_control;
  if (req.url.path ~ "^/women-of-the-year-2024/(_next|assets|images)"){
    set beresp.ttl = 1y;
  }
}

##############################
### woty2025 App ###
##############################

sub gnt_woty2025_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:woty2025 = "1";
  set req.http.Gannett-Debug-Path-Item = "woty2025";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "woty2025.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_woty2025_backend {
  set req.http.Gannett-Debug-Path-Item = "woty2025 backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/women-of-the-year-2025";
  set req.backend = F_woty2025_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_woty2025_east;
  } else {
      set req.backend = F_woty2025_west;
  }
  # check that the backend is healthy
  if(req.backend == F_woty2025_east && !req.backend.healthy) {
      set req.backend = F_woty2025_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_woty2025_west && !req.backend.healthy) {
      set req.backend = F_woty2025_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_woty2025_fetch {
  call keep_origin_cache_control;
  if (req.url.path ~ "^/women-of-the-year-2025/(_next|assets|images)"){
    set beresp.ttl = 1y;
  }
}

##############################
### womenssports App ###
##############################

sub gnt_womenssports_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:womenssports = "1";
  set req.http.Gannett-Debug-Path-Item = "womenssports";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "womenssports.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_womenssports_backend {
  set req.http.Gannett-Debug-Path-Item = "womenssports backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/womens-sports";
  set req.backend = F_womenssports_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_womenssports_east;
  } else {
      set req.backend = F_womenssports_west;
  }
  # check that the backend is healthy
  if(req.backend == F_womenssports_east && !req.backend.healthy) {
      set req.backend = F_womenssports_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_womenssports_west && !req.backend.healthy) {
      set req.backend = F_womenssports_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_womenssports_fetch {
  call keep_origin_cache_control;
  if (req.url.path ~ "^/womens-sports/(_next|assets|images)"){
    set beresp.ttl = 1y;
  }
}

##############################
### holidaymarketplace App ###
##############################

sub gnt_holiday_marketplace_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:holiday-marketplace = "1";
  set req.http.Gannett-Debug-Path-Item = "holiday-marketplace";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "holidaymarketplace.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_holiday_marketplace_backend {
  set req.http.Gannett-Debug-Path-Item = "holiday-marketplace backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/holiday-marketplace";
  set req.backend = F_holidaymarketplace_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_holidaymarketplace_east;
  } else {
      set req.backend = F_holidaymarketplace_west;
  }
  # check that the backend is healthy
  if(req.backend == F_holidaymarketplace_east && !req.backend.healthy) {
      set req.backend = F_holidaymarketplace_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_holidaymarketplace_west && !req.backend.healthy) {
      set req.backend = F_holidaymarketplace_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_holiday_marketplace_fetch {
  # preserve cache-control setting
  call keep_origin_cache_control;
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
  if (req.url.path ~ "^/holiday-marketplace/(_next|assets|fonts|images)"){
    set beresp.ttl = 1y;
  }
  else {
    set beresp.ttl = 5m;
    set beresp.http.Cache-Control = "max-age=60";
  }
}

##############################
### home_improvement App ###
##############################

sub gnt_home_improvement_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:home-improvement = "1";
  set req.http.Gannett-Debug-Path-Item = "home-improvement";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "home-improvement.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_home_improvement_backend {
  set req.http.Gannett-Debug-Path-Item = "home-improvement backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/home-improvement";
  set req.backend = F_home_improvement_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_home_improvement_east;
  } else {
      set req.backend = F_home_improvement_west;
  }
  # check that the backend is healthy
  if(req.backend == F_home_improvement_east && !req.backend.healthy) {
      set req.backend = F_home_improvement_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_home_improvement_west && !req.backend.healthy) {
      set req.backend = F_home_improvement_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_home_improvement_fetch {
  call keep_origin_cache_control;
  if (req.url.path ~ "^/home-improvement/(_next|assets|images)"){
    set beresp.ttl = 1y;
  }
}

##############################
### Lottery App ###
##############################

sub gnt_lottery_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:lottery = "1";
  set req.http.Gannett-Debug-Path-Item = "lottery";
  call shared_helpers_general_record_object_path;
  if(req.http.host ~ "^origin-staging") {
    set req.http.x-origin-expected-host = "origin-staging-www.usatoday.com";
  } else {
    set req.http.x-origin-expected-host = "www.usatoday.com";
  }

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_lottery_backend {
  set req.http.Gannett-Debug-Path-Item = "lottery backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/lottery";
  set req.backend = F_lottery_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_lottery_east;
  } else {
      set req.backend = F_lottery_west;
  }
  # check that the backend is healthy
  if(req.backend == F_lottery_east && !req.backend.healthy) {
      set req.backend = F_lottery_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_lottery_west && !req.backend.healthy) {
      set req.backend = F_lottery_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_lottery_fetch {
  if (beresp.http.Surrogate-Control !~ "max-age"){
      if (beresp.http.Surrogate-Control ~ "no-cache|no-store"){
      return(pass);
    } else {
      set beresp.ttl = 60s;
    }
  } else {
    set beresp.ttl = 60s;
  }
  # Specifically set browser cache rules for fonts
  if (req.url.path ~ "^/lottery/(webfonts|_next)") {
      set beresp.http.Cache-Control = "public, max-age=31536000, immutable";
      set beresp.ttl = 3600s;
      call keep_origin_cache_control;
  }
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
}

##############################
### Comics App ###
##############################

sub gnt_comics_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:comics = "1";
  set req.http.Gannett-Debug-Path-Item = "comics";
  call shared_helpers_general_record_object_path;
  if(req.http.host ~ "^origin-staging") {
    set req.http.x-origin-expected-host = "origin-staging-www.usatoday.com";
  } else {
    set req.http.x-origin-expected-host = "www.usatoday.com";
  }

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_comics_backend {
  set req.http.Gannett-Debug-Path-Item = "comics backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/comics";
  set req.backend = F_comics_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_comics_east;
  } else {
      set req.backend = F_comics_west;
  }
  # check that the backend is healthy
  if(req.backend == F_comics_east && !req.backend.healthy) {
      set req.backend = F_comics_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_comics_west && !req.backend.healthy) {
      set req.backend = F_comics_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_comics_fetch {
  if (beresp.http.Surrogate-Control !~ "max-age"){
      if (beresp.http.Surrogate-Control ~ "no-cache|no-store"){
      return(pass);
    } else {
      set beresp.ttl = 60s;
    }
  } else {
    set beresp.ttl = 60s;
  }
  # Specifically set browser cache rules for fonts
  if (req.url.path ~ "^/comics/(webfonts|_next)") {
      set beresp.http.Cache-Control = "public, max-age=31536000, immutable";
      set beresp.ttl = 3600s;
      call keep_origin_cache_control;
  }
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
}

############################
### Coupons Deals App ###
############################

sub gnt_deals_recv {
  if (req.url.path ~ "/$") {
    set req.http.Gannett-Debug-Path-Item = "deals slash redirect";
    call shared_helpers_general_record_object_path;
    set req.url = regsub(req.url.path, "/$", "");
    set req.http.x-Redir-Url = "https://" req.http.host req.url.path;
    if (req.url.qs != "") {
      set req.http.x-Redir-Url = req.http.x-Redir-Url "?" req.url.qs;
    }
    error 701 req.http.x-Redir-Url;
  }
  set req.http.Gannett-Debug-Path-Item = "coupons /deals";
  call shared_helpers_general_record_object_path;
  set req.http.Gannett-Custom:deals = "1";
  set req.http.Authorization = "Basic dXNhdG9kYXktdHNnOnVzYXRvZGF5LXRzZy0yMDI1";
  set req.http.x-origin-expected-host = "d2x1ti9cf8k8y9.cloudfront.net";
  set req.http.X-Forwarded-For = client.ip;
}

sub gnt_deals_backend {
  set req.http.x-append-surrogate = "/deals";
  set req.backend = F_deals_backend;
  set req.http.Gannett-Debug-Path-Item = "DEALS backend";
  call shared_helpers_general_record_object_path;
}

sub gnt_deals_fetch {
  set beresp.cacheable = true;
  call keep_origin_cache_control;
  # set security policy headers
  set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none'";
  set beresp.http.Content-Security-Policy-Report-Only = "script-src https: blob: 'unsafe-inline' 'unsafe-eval' 'self';base-uri 'self';report-uri https://reporting-api.gannettinnovation.com;report-to default";
  set beresp.http.X-Content-Type-Options = "nosniff";
  set beresp.http.X-Frame-Options = "deny";
  set beresp.http.X-XSS-Protection = "1; mode=block";
  if (req.url.path ~ "^/deals/widget/") {
    unset beresp.http.X-Frame-Options;
    set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors https:;object-src 'none'";
  }
  set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
  set beresp.http.Permissions-Policy = "camera=(),display-capture=(),geolocation=(),microphone=(),payment=(),usb=(),xr-spatial-tracking=()";
  set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
  set beresp.http.Cross-Origin-Resource-Policy = "same-origin";
  return(pass);
}

sub gnt_deals_deliver {
  if(std.prefixof(resp.http.Content-Type, "text/html")) {
    set resp.http.Cache-Control = "private,no-cache";
  }
}

############################
### XPR Media App ###
############################

sub gnt_xpr_media_recv {
  # redirect if no trailing slash
  call redirect_on_missing_trailing_slash;
  set req.http.Gannett-Custom:xpr_media = "1";
  set req.http.Gannett-Debug-Path-Item = "xpr_media";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "usatoday.xpr-gannett.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_xpr_media_backend {
  set req.http.Gannett-Debug-Path-Item = "xpr_media backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "/press-release";
  set req.backend = F_xpr_gannett_com;
}

sub gnt_xpr_media_fetch {
  if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
      return(pass);
  }
  set beresp.ttl = 300s;
}

sub gnt_xpr_media_deliver {
  if(std.prefixof(resp.http.Content-Type, "text/html")) {
    set resp.http.Cache-Control = "private,no-cache";
  }
}

##############################
### pets hub ###
##############################

sub gnt_petshub_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:petshub = "1";
  set req.http.Gannett-Debug-Path-Item = "pets hub";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "petshub.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_petshub_backend {
  set req.http.Gannett-Debug-Path-Item = "petshub backend:set";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "petshub";
  set req.backend = F_petshub_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_petshub_east;
  } else {
      set req.backend = F_petshub_west;
  }
  # check that the backend is healthy
  if(req.backend == F_petshub_east && !req.backend.healthy) {
      set req.backend = F_petshub_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_petshub_west && !req.backend.healthy) {
      set req.backend = F_petshub_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_petshub_fetch {
  call keep_origin_cache_control;
  if(std.prefixof(beresp.http.Content-Type, "text/html")) {  # Cache the HTML pages on Fastly
    set beresp.ttl = 120m;
  } elseif (req.url.path ~ "^/pets-animals/(webfonts|_next|favicon)") {     # Specifically set browser cache rules for fonts
    set beresp.ttl = 30d;
  } else {
    set beresp.ttl = 60m;
  }

  # set security policy headers
  set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none'";
  set beresp.http.Content-Security-Policy-Report-Only = "script-src https: blob: 'unsafe-inline' 'unsafe-eval' 'self';base-uri 'self';report-uri https://reporting-api.gannettinnovation.com";
  set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
  set beresp.http.Permissions-Policy = "bluetooth=(),camera=(),display-capture=(),geolocation=(),hid=(),identity-credentials-get=(),local-fonts=(),microphone=(),midi=(),otp-credentials=(),payment=(),publickey-credentials-create=(),publickey-credentials-get=(),serial=(),usb=(),window-management=(),xr-spatial-tracking=()";
  set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
  set beresp.http.X-Content-Type-Options = "nosniff";
  set beresp.http.X-Frame-Options = "deny";
  set beresp.http.X-XSS-Protection = "1; mode=block";
  set beresp.http.Cross-Origin-Resource-Policy = "same-origin";


  # Dont PASS the HTML page because we are caching it on Fastly
  if( !(std.prefixof(beresp.http.Content-Type, "text/html")) && beresp.http.Cache-Control ~ "(private|no-store|no-cache)" ) {
    return(pass);
  }
}

sub gnt_petshub_deliver {
  if(std.prefixof(resp.http.Content-Type, "text/html")) {
    set resp.http.Cache-Control = "private,no-cache";
  }
}

##############################
### /bot-detection ###
##############################

sub gnt_bot_detection_recv {
  call select_geo_specific_region;
  set req.http.Gannett-Custom:bot-detection = "";
  set req.http.Gannett-Debug-Path-Item = "/bot-detection";
  call shared_helpers_general_record_object_path;

  set req.http.x-origin-expected-host = "www.usatoday.com";

  # keep old setting without force br
  call shared_helpers_frontend_keep_gzip_override;
}

sub gnt_bot_detection_backend {
  set req.http.Gannett-Debug-Path-Item = "bot-detection backend";
  call shared_helpers_general_record_object_path;
  set req.http.x-append-surrogate = "bot-detection";
  set req.backend = F_bot_detection_east;

  # Geo Based Load Balancing
  call announce_region_selection;
  if (req.http.gnt-client:geo-region == "east") {
      set req.backend = F_bot_detection_east;
  } else {
      set req.backend = F_bot_detection_west;
  }
  # check that the backend is healthy
  if(req.backend == F_bot_detection_east && !req.backend.healthy) {
      set req.backend = F_bot_detection_west;
      set req.http.Gannett-Debug-Path-Item = "east unhealthy";
      call shared_helpers_general_record_object_path;
  } else if(req.backend == F_bot_detection_west && !req.backend.healthy) {
      set req.backend = F_bot_detection_east;
      set req.http.Gannett-Debug-Path-Item = "west unhealthy";
      call shared_helpers_general_record_object_path;
  }
}

sub gnt_bot_detection_fetch {
  call keep_origin_cache_control;
  if(std.prefixof(beresp.http.Content-Type, "text/html")) {  # Cache the HTML pages on Fastly
    set beresp.ttl = 30d;
  } elseif (req.url.path ~ "^/bot-detection/(webfonts|_next|favicon)") {     # Specifically set browser cache rules for fonts
    set beresp.ttl = 30d;
  } else {
    set beresp.ttl = 30d;
  }

  # set security policy headers
  set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none'";
  set beresp.http.Content-Security-Policy-Report-Only = "script-src https: blob: 'unsafe-inline' 'unsafe-eval' 'self';base-uri 'self';report-uri https://reporting-api.gannettinnovation.com";
  set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
  set beresp.http.Permissions-Policy = "bluetooth=(),camera=(),display-capture=(),geolocation=(),hid=(),identity-credentials-get=(),local-fonts=(),microphone=(),midi=(),otp-credentials=(),payment=(),publickey-credentials-create=(),publickey-credentials-get=(),serial=(),usb=(),window-management=(),xr-spatial-tracking=()";
  set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
  set beresp.http.X-Content-Type-Options = "nosniff";
  set beresp.http.X-Frame-Options = "deny";
  set beresp.http.X-XSS-Protection = "1; mode=block";
  set beresp.http.Cross-Origin-Resource-Policy = "same-origin";
}

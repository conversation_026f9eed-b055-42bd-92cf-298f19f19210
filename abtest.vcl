# Test bucketing and setting of X-AbVariant.
sub abtest_recv_headers {

    # Create stash of a/b test-related cookies to be used as a reference since the request <PERSON><PERSON> gets emptied in
    # WALL-LY processing (see main.vcl > recv_sanitize_cookie_header)
    call abtest_stash_cookies;

    # Shared snippet assigns values to req.http.Gannett-AB:gnt-ub and req.http.Gannett-AB:gnt-sb
    call shared_ab_testing_assign_user_test_values;

    # Headers from shared snippet are then convert to integers and stored as local variables for computation
    declare local var.user_bucket INTEGER;
    declare local var.user_slot INTEGER;
    set var.user_bucket = std.atoi(req.http.Gannett-AB:gnt-ub);
    set var.user_slot = std.atoi(req.http.Gannett-AB:gnt-sb);

    # Create a mock test to help verify that X-AbVariant-based testing is working correctly
    if (req.http.Gannett-Debug) {  
      if (req.url.path == "/story/tech/2020/02/03/microsoft-teams-issues-fix/4644138002/") {
        if (req.http.Cookie:tosba) {
          set req.http.X-AbVariant = req.http.Cookie:tosba;
        } elsif (var.user_slot == 6) {
          set req.http.X-AbVariant = "tosba_a";
        } elsif (var.user_slot == 7) {
          set req.http.X-AbVariant = "tosba_b";
        }
      }
    }

    ######################
    ## Tangent A/B tests
    ######################
    if (req.http.x-origin-expected-host == "tangent.usatoday.com") {

      #######################################
      ## EX.co videoplayer A/B test for USAT
      #######################################
      if (
        client.geo.country_code == "US"
        && (req.url.path ~ "^/story/" || req.url.path ~ "^/videos/" || req.url.ext == "") #inclusions
        && req.url.qs !~ "gnt-cfr" #exclusion
        && req.url.path !~ "^/sponsor-story/" #exclusion
        && req.url.path !~ "^/special/contributor-content/" #exclusion
        && req.url.path !~ "^/picture-gallery/" #exclusion
        && req.url.path !~ "^/contact/" #exclusion
        && req.url.path !~ "^/for-you/" #exclusion
        && req.url.path !~ "^/manage-topics/" #exclusion
        && req.url.path !~ "^/contributor-content/" #exclusion
        && req.url.path !~ "^/errors/" #exclusion
        && req.url.path !~ "^/staff/" #exclusion
        && req.url.path !~ "^/sitemap/" #exclusion
        && req.url.path !~ "^/story/sponsor-story/" #exclusion
        && req.url.path !~ "^/story/special/contributor-content/" #exclusion
        && req.url.path !~ "^/videos/sponsor-story/" #exclusion
      ){
        #configuration for gnt-ub value
        if (var.user_bucket >= 2 && var.user_bucket <= 100) {
          if (req.http.X-AbVCfg) {
            if (!req.http.Gannett-FF) {
              set req.http.X-AbVCfg = req.http.X-AbVCfg "," "{%22v%22:%220060_50_B%22,%22s%22:2,%22e%22:100}";
            }
          } else {
            set req.http.X-AbVCfg = "{%22v%22:%220060_50_B%22,%22s%22:2,%22e%22:100}";
          }
        } else {
          if (req.http.X-AbVCfg) {
            if (!req.http.Gannett-FF) {
              set req.http.X-AbVCfg = req.http.X-AbVCfg "," "{%22v%22:%220060_50_A%22,%22s%22:1,%22e%22:1}";
            }
          } else {
            set req.http.X-AbVCfg = "{%22v%22:%220060_50_A%22,%22s%22:1,%22e%22:1}";
          }
        }
      }
    }

    #############################################################################
    ## X-AbVCfg (ORD-3005)
    ##
    ## For AB tests that don't require front-end app changes (e.g. DFP / analytics),
    ## use the X-AbVCfg header (Tangent only) instead of X-AbVariant such that the AB test
    ## is passed through to the client side code without unnecessarily increasing
    ## the size of the cache for the same server side response.
    ##
    ## For other platforms (e.g. CP, UW) that the same type of test needs to run on - continue to
    ## use the X-AbVariant header unless X-AbVCfg becomes supported on those platforms.
    ##
    ## AB Test metadata format
    ##
    ## String containing multiple comma delimited JSON objects, representing each matched test to run on the page.
    ## Code in abtest_recv_complete will convert header into a JSON array.
    ## v = variant value
    ## s = gnt_ub start value (inclusive)
    ## e = gnt_ub end value (inclusive)
    ##
    ## To add AB test specific surrogate keys use:  req.http.Gannett-Custom:X-AbVCfg-sk to ensure they get preserved downstream
    ##
    ## See example test below...
    #############################################################################

    ## EXAMPLE X-AbVCfg test - DO NOT UNCOMMENT
    # if (
    #   req.url.path ~ "/some-path"
    # ) {
    #   # when intended bakend is Tangent - use X-AbVCfg
    #   if (req.http.x-origin-expected-host == "tangent.usatoday.com") {
    #     # preserve additional matched test(s)
    #     if (req.http.X-AbVCfg) {
    #       if (!req.http.Gannett-FF) {
    #         set req.http.X-AbVCfg = req.http.X-AbVCfg "," "{%22v%22:%22abvcfg_test_1_variant1%22,%22s%22:1,%22e%22:20},{%22v%22:%22abvcfg_test_1_variant2%22,%22s%22:21,%22e%22:40}";
    #       }
    #     } else {
    #       set req.http.X-AbVCfg = "{%22v%22:%22abvcfg_test_1_variant1%22,%22s%22:1,%22e%22:20},{%22v%22:%22abvcfg_test_1_variant2%22,%22s%22:21,%22e%22:40}";
    #     }
    #     # flag test specific surrogate-key to be successfully applied in abtest_recv_complete
    #     if (req.http.Gannett-Custom:X-AbVCfg-sk) {
    #       set req.http.Gannett-Custom:X-AbVCfg-sk = req.http.Gannett-Custom:X-AbVCfg-sk " abvcfg_test_1";
    #     } else {
    #       set req.http.Gannett-Custom:X-AbVCfg-sk = "abvcfg_test_1";
    #     }
    #   # for CP / UW continue to use X-AbVariant for same test
    #   } else {
    #     if (var.user_slot >= 1 && var.user_slot <= 20) {
    #       set req.http.X-AbVariant = "abvcfg_test_1_variant1";
    #     } else if (var.user_slot >= 21 && var.user_slot <= 40) {
    #       set req.http.X-AbVariant = "abvcfg_test_1_variant2";
    #     }
    #   }
    # }

}

# Backend overrides should happen here.
sub abtest_recv_complete {

    #############################################################################
    ## X-AbVCfg Do Not Modify (ORD-3005)
    #############################################################################
    if (req.http.X-AbVCfg) {
      # prefer X-AbVariant over X-AbVCfg as they are mutually exclusive
      if (req.http.X-AbVariant) {
        unset req.http.X-AbVCfg;
      # certain paths won't support req.http.X-AbVCfg - unset here to prevent unecessary increase of cache
      } elseif (
        req.url.ext != "" ||
        req.url.path ~ "^/tangs(vc|tatic)/" ||
        req.url.path ~ "^/.well-known" ||
        req.url.path ~ "apple-app-site-association$"
      ) {
        unset req.http.X-AbVCfg;
      } else {
        # must be done only once
        if (
          !req.http.Gannett-FF ||
          (
            req.restarts > 0 && 
            !std.prefixof(req.http.X-AbVCfg, "[") &&
            !std.suffixof(req.http.X-AbVCfg, "]")
          )
        ) {
          # convert to JSON array
          set req.http.X-AbVCfg = "[" req.http.X-AbVCfg "]";
        }
        # surrogate keys
        if (req.http.x-append-surrogate) {
          set req.http.x-append-surrogate = req.http.x-append-surrogate " x-abvcfg " req.http.Gannett-Custom:X-AbVCfg-sk;
        } else {
          set req.http.x-append-surrogate = "x-abvcfg " req.http.Gannett-Custom:X-AbVCfg-sk;
        }
      }
      set req.http.Gannett-Debug-Path-Item = "X-AbVCfg: " req.http.X-AbVCfg;
      call shared_helpers_general_record_object_path;
    }

}

# Cookie setting and cleanup.
sub abtest_deliver {
  ## DO NOT MODIFY
  #######################################
  ## A/A test to verify slot-based
  #######################################
  if (
    req.http.Gannett-Debug &&
    !req.http.Gannett-AB-Cookies:tosba &&
    req.http.X-AbVariant ~ "^tosba_"
  ) {
    add resp.http.Set-Cookie = "tosba=" req.http.X-AbVariant "; path=/; expires=" now + 30d ";";
  }

  # Shared snippet sets gnt_ub, gnt_sb, and X-AbVariant response headers
  call shared_ab_testing_set_cookies_and_headers;
  call abtest_deliver_cleanup;

  ## HP4U AB-test (remove when over)
  if (req.http.Gannett-Custom:X-AbHP4U == "taboolabot") {
    set resp.http.Cache-Control = "private,no-store";
  }
}

# Expire cookies from previous tests. Default cookie expiration is currently 180 days.
sub abtest_deliver_cleanup {
  declare local var.cookie_expire STRING;
  set var.cookie_expire = "=deleted; expires=Thu, 01 Jan 1970 00:00:01 GMT; Max-Age=0; path=/;";
  ## Example:
  ## if (req.http.Cookie:to203) {
  ##    add resp.http.Set-Cookie = "to203" var.cookie_expire;
  ## }
}

sub abtest_fetch {
  call shared_ab_testing_clear_variant_headers;
}

sub abtest_stash_cookies {
  if (std.strlen(req.http.Cookie:gnt_ub) > 0) {
    set req.http.Gannett-AB-Cookies:gnt_ub = req.http.Cookie:gnt_ub;
  }
  if (std.strlen(req.http.Cookie:gnt_sb) > 0) {
    set req.http.Gannett-AB-Cookies:gnt_sb = req.http.Cookie:gnt_sb;
  }
  if (req.http.Gannett-Debug && req.http.Cookie:tosba) {
    set req.http.Gannett-AB-Cookies:tosba = req.http.Cookie:tosba;
  }
}

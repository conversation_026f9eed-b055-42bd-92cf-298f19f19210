name: Deploy Origin Staging

on:
  push:
    branches:
      - 'main'
      - 'origin-staging-**'
      - '!origin-staging-1-**'
      - '!origin-staging-2-**'
      - '!origin-staging-3-**'
      - '!origin-staging-4-**'
    paths-ignore:
      - ".github/**"
  workflow_dispatch:

jobs:
  fastly-vcl-deploy:
    uses: GannettDigital/platform-engineering-shared-github-actions/.github/workflows/fastly-vcl-services-deploy.yaml@main
    with:
      environment: origin-staging
      int_runner_name: "cet-beefy"
    secrets: inherit
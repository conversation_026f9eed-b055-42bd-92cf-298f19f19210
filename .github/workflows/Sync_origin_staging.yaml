name: Sync Origin Staging
run-name: Sync-${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: choice
        description: Choose environment
        options:
          - origin-staging
          - origin-staging-1
          - origin-staging-2
          - origin-staging-3
          - origin-staging-4
          - ci
          - staging

jobs:
  fastly-vcl-deploy:
    uses: GannettDigital/platform-engineering-shared-github-actions/.github/workflows/fastly-origin-staging-sync-deploy.yaml@main
    with:
      environment: ${{ inputs.environment }}
    secrets: inherit